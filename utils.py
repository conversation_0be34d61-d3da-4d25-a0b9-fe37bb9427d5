import sys
from datetime import datetime
import os

# Import packages with error handling
try:
    import cv2
except ImportError:
    cv2 = None

try:
    import numpy as np
except ImportError:
    np = None

try:
    import matplotlib.pyplot as plt
except ImportError:
    plt = None

class EmotionUtils:
    @staticmethod
    def test_camera(camera_index=0):
        """Test if camera is working properly"""
        print(f"Testing camera {camera_index}...")

        if cv2 is None:
            print("❌ OpenCV (cv2) is not installed. Please install it with: pip install opencv-python")
            return False

        try:
            cap = cv2.VideoCapture(camera_index)
            if not cap.isOpened():
                print(f"❌ Error: Cannot access camera {camera_index}")
                return False

            print("Camera is accessible. Testing frame capture...")
            ret, frame = cap.read()

            if ret:
                print(f"✅ Camera working! Frame size: {frame.shape}")
                print("Showing test frame for 3 seconds...")
                cv2.imshow('Camera Test', frame)
                cv2.waitKey(3000)
                cv2.destroyAllWindows()
            else:
                print("❌ Cannot capture frame from camera")
                cap.release()
                return False

            cap.release()
            return True
        except Exception as e:
            print(f"❌ Error testing camera: {e}")
            return False
    
    @staticmethod
    def list_available_cameras():
        """List all available cameras"""
        print("Scanning for available cameras...")

        if cv2 is None:
            print("❌ OpenCV (cv2) is not installed. Please install it with: pip install opencv-python")
            return []

        available_cameras = []

        try:
            for i in range(10):  # Check first 10 camera indices
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        available_cameras.append(i)
                        print(f"✅ Camera {i}: Available")
                    cap.release()
                else:
                    print(f"❌ Camera {i}: Not available")

            if not available_cameras:
                print("No cameras found!")
            else:
                print(f"Available cameras: {available_cameras}")

            return available_cameras
        except Exception as e:
            print(f"❌ Error scanning cameras: {e}")
            return []
    
    @staticmethod
    def create_emotion_chart(emotions_detected):
        """Create a chart showing detected emotions over time"""
        if not emotions_detected:
            print("No emotion data to chart")
            return

        if plt is None:
            print("❌ Matplotlib is not installed. Please install it with: pip install matplotlib")
            return

        try:
            emotions = list(emotions_detected.keys())
            counts = list(emotions_detected.values())

            plt.figure(figsize=(10, 6))
            plt.bar(emotions, counts, color=['red', 'brown', 'purple', 'green', 'gray', 'blue', 'yellow'])
            plt.title('Detected Emotions Distribution')
            plt.xlabel('Emotions')
            plt.ylabel('Detection Count')
            plt.xticks(rotation=45)
            plt.tight_layout()

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"emotion_chart_{timestamp}.png"
            plt.savefig(filename)
            plt.show()
            print(f"✅ Chart saved as {filename}")
        except Exception as e:
            print(f"❌ Error creating chart: {e}")
    
    @staticmethod
    def check_dependencies():
        """Check if all required dependencies are installed"""
        print("Checking required dependencies...")

        dependencies = {
            'opencv-python': 'cv2',
            'numpy': 'numpy',
            'tensorflow': 'tensorflow',
            'matplotlib': 'matplotlib',
            'pillow': 'PIL'
        }

        missing_packages = []

        for package_name, import_name in dependencies.items():
            try:
                if import_name == 'cv2':
                    import cv2
                    print(f"✅ {package_name}: Installed")
                elif import_name == 'numpy':
                    import numpy
                    print(f"✅ {package_name}: Installed")
                elif import_name == 'tensorflow':
                    import tensorflow
                    print(f"✅ {package_name}: Installed")
                elif import_name == 'matplotlib':
                    import matplotlib
                    print(f"✅ {package_name}: Installed")
                elif import_name == 'PIL':
                    from PIL import Image
                    print(f"✅ {package_name}: Installed")
            except ImportError:
                print(f"❌ {package_name}: Missing")
                missing_packages.append(package_name)

        if missing_packages:
            print(f"\n❌ Missing packages: {missing_packages}")
            print("Install them using: pip install -r requirements.txt")
            return False
        else:
            print("\n✅ All dependencies are installed!")
            return True
    
    @staticmethod
    def calibrate_detection_sensitivity():
        """Help users calibrate face detection sensitivity"""
        print("Starting face detection calibration...")
        print("This will help you adjust detection parameters for your lighting conditions")

        if cv2 is None:
            print("❌ OpenCV (cv2) is not installed. Please install it with: pip install opencv-python")
            return

        try:
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                print("❌ Cannot access camera for calibration")
                return

            # Different sensitivity settings to test
            scale_factors = [1.1, 1.2, 1.3, 1.4]
            min_neighbors = [3, 4, 5, 6]

            current_scale = 0
            current_neighbor = 0

            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

            print("Use 's' to cycle scale factor, 'n' to cycle neighbors, 'q' to quit")

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame = cv2.flip(frame, 1)
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

                scale_factor = scale_factors[current_scale]
                min_neighbor = min_neighbors[current_neighbor]

                faces = face_cascade.detectMultiScale(
                    gray,
                    scaleFactor=scale_factor,
                    minNeighbors=min_neighbor
                )

                # Draw detected faces
                for (x, y, w, h) in faces:
                    cv2.rectangle(frame, (x, y), (x+w, y+h), (255, 0, 0), 2)

                # Display current settings
                cv2.putText(frame, f'Scale: {scale_factor}, Neighbors: {min_neighbor}',
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(frame, f'Faces detected: {len(faces)}',
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(frame, "Press 's' for scale, 'n' for neighbors, 'q' to quit",
                           (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                cv2.imshow('Face Detection Calibration', frame)

                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    current_scale = (current_scale + 1) % len(scale_factors)
                    print(f"Scale factor: {scale_factors[current_scale]}")
                elif key == ord('n'):
                    current_neighbor = (current_neighbor + 1) % len(min_neighbors)
                    print(f"Min neighbors: {min_neighbors[current_neighbor]}")

            cap.release()
            cv2.destroyAllWindows()

            print(f"✅ Recommended settings:")
            print(f"Scale Factor: {scale_factors[current_scale]}")
            print(f"Min Neighbors: {min_neighbors[current_neighbor]}")

        except Exception as e:
            print(f"❌ Error during calibration: {e}")
            if 'cap' in locals():
                cap.release()
            cv2.destroyAllWindows()

def main():
    """Utility functions menu"""
    utils = EmotionUtils()
    
    while True:
        print("\n=== Emotion Detection Utilities ===")
        print("1. Test camera")
        print("2. List available cameras")
        print("3. Check dependencies")
        print("4. Calibrate detection sensitivity")
        print("5. Exit")
        
        choice = input("Enter your choice (1-5): ").strip()
        
        if choice == '1':
            camera_index = input("Enter camera index (default 0): ").strip()
            camera_index = int(camera_index) if camera_index.isdigit() else 0
            utils.test_camera(camera_index)
        elif choice == '2':
            utils.list_available_cameras()
        elif choice == '3':
            utils.check_dependencies()
        elif choice == '4':
            utils.calibrate_detection_sensitivity()
        elif choice == '5':
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
