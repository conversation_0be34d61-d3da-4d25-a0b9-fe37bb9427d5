import time
import argparse
import sys

# Import packages with error handling
try:
    import cv2
except ImportError:
    print("❌ Error: OpenCV (cv2) is not installed.")
    print("Please install it with: pip install opencv-python")
    print("Or run: python install.py")
    sys.exit(1)

try:
    import numpy as np
except ImportError:
    print("❌ Error: NumPy is not installed.")
    print("Please install it with: pip install numpy")
    print("Or run: python install.py")
    sys.exit(1)

try:
    from emotion_detector import EmotionDetector
except ImportError as e:
    print("❌ Error: Could not import EmotionDetector.")
    print(f"Details: {e}")
    print("Please ensure all dependencies are installed by running: python install.py")
    sys.exit(1)

class RealTimeEmotionDetection:
    def __init__(self, camera_index=0):
        self.camera_index = camera_index
        self.emotion_detector = EmotionDetector()
        self.cap = None
        self.is_running = False
        
    def initialize_camera(self):
        """Initialize the camera"""
        try:
            self.cap = cv2.VideoCapture(self.camera_index)
            if not self.cap.isOpened():
                print(f"Error: Could not open camera {self.camera_index}")
                return False
            
            # Set camera properties for better performance
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            print("Camera initialized successfully")
            return True
        except Exception as e:
            print(f"Error initializing camera: {e}")
            return False
    
    def run_detection(self):
        """Main loop for real-time emotion detection"""
        if not self.initialize_camera():
            return
        
        self.is_running = True
        print("Starting real-time emotion detection...")
        print("Press 'q' to quit, 's' to save current frame, 'p' to pause/resume")
        
        paused = False
        frame_count = 0
        start_time = time.time()
        
        try:
            while self.is_running:
                if not paused:
                    ret, frame = self.cap.read()
                    if not ret:
                        print("Error: Could not read frame from camera")
                        break
                    
                    # Flip frame horizontally for mirror effect
                    frame = cv2.flip(frame, 1)
                    
                    # Detect faces and emotions
                    faces, gray = self.emotion_detector.detect_faces(frame)
                    
                    # Draw emotion results
                    frame = self.emotion_detector.draw_emotion_results(frame, faces, gray)
                    
                    # Add FPS counter
                    frame_count += 1
                    elapsed_time = time.time() - start_time
                    if elapsed_time > 0:
                        fps = frame_count / elapsed_time
                        cv2.putText(frame, f'FPS: {fps:.1f}', (10, 30), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    
                    # Add instructions
                    cv2.putText(frame, "Press 'q' to quit, 's' to save, 'p' to pause", 
                               (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                
                # Display the frame
                cv2.imshow('Real-Time Emotion Detection', frame)
                
                # Handle key presses
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    self.save_frame(frame)
                elif key == ord('p'):
                    paused = not paused
                    print(f"Detection {'paused' if paused else 'resumed'}")
                
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        except Exception as e:
            print(f"Error during detection: {e}")
        finally:
            self.cleanup()
    
    def save_frame(self, frame):
        """Save current frame with timestamp"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"emotion_detection_{timestamp}.jpg"
        cv2.imwrite(filename, frame)
        print(f"Frame saved as {filename}")
    
    def cleanup(self):
        """Clean up resources"""
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        print("Resources cleaned up")

def main():
    parser = argparse.ArgumentParser(description='Real-time Emotion Detection')
    parser.add_argument('--camera', type=int, default=0, help='Camera index (default: 0)')
    parser.add_argument('--test', action='store_true', help='Test camera connection')
    
    args = parser.parse_args()
    
    if args.test:
        # Test camera connection
        cap = cv2.VideoCapture(args.camera)
        if cap.isOpened():
            print(f"Camera {args.camera} is working")
            ret, frame = cap.read()
            if ret:
                print(f"Frame size: {frame.shape}")
            cap.release()
        else:
            print(f"Camera {args.camera} is not accessible")
        return
    
    # Run emotion detection
    detector = RealTimeEmotionDetection(camera_index=args.camera)
    detector.run_detection()

if __name__ == "__main__":
    main()
