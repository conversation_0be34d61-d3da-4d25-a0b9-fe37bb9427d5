@echo off
echo ========================================
echo Real-Time Emotion Detection System
echo Installation Script for Windows
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python found! Running installation script...
echo.

python install.py

echo.
echo Installation script completed.
pause
