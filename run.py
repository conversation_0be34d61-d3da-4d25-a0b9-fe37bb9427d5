#!/usr/bin/env python3
"""
Quick start script for Real-Time Emotion Detection System
This script provides a simple menu to run different components of the system.
"""

import subprocess
import sys
import os

def show_menu():
    """Display the main menu"""
    print("\n" + "="*50)
    print("🎭 Real-Time Emotion Detection System")
    print("="*50)
    print("1. 🚀 Start Emotion Detection")
    print("2. 🔧 Run Utilities")
    print("3. 📷 Test Camera")
    print("4. 🧠 Setup Model")
    print("5. 📊 Check System Status")
    print("6. 📖 View README")
    print("7. ❌ Exit")
    print("="*50)

def start_emotion_detection():
    """Start the main emotion detection program"""
    print("\n🚀 Starting emotion detection...")
    print("Controls: 'q' to quit, 's' to save frame, 'p' to pause")
    print("Press Ctrl+C to stop if needed\n")
    
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\n✋ Emotion detection stopped by user")
    except FileNotFoundError:
        print("❌ main.py not found. Please ensure all files are in the current directory.")
    except Exception as e:
        print(f"❌ Error running emotion detection: {e}")

def run_utilities():
    """Run the utilities script"""
    print("\n🔧 Starting utilities...")
    try:
        subprocess.run([sys.executable, "utils.py"])
    except FileNotFoundError:
        print("❌ utils.py not found.")
    except Exception as e:
        print(f"❌ Error running utilities: {e}")

def test_camera():
    """Quick camera test"""
    print("\n📷 Testing camera...")
    try:
        subprocess.run([sys.executable, "main.py", "--test"])
    except FileNotFoundError:
        print("❌ main.py not found.")
    except Exception as e:
        print(f"❌ Error testing camera: {e}")

def setup_model():
    """Setup the emotion detection model"""
    print("\n🧠 Setting up model...")
    try:
        subprocess.run([sys.executable, "setup_model.py"])
    except FileNotFoundError:
        print("❌ setup_model.py not found.")
    except Exception as e:
        print(f"❌ Error setting up model: {e}")

def check_system_status():
    """Check system status and dependencies"""
    print("\n📊 Checking system status...")
    
    # Check Python version
    print(f"Python version: {sys.version}")
    
    # Check if main files exist
    required_files = ["main.py", "emotion_detector.py", "utils.py", "setup_model.py", "requirements.txt"]
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}: Found")
        else:
            print(f"❌ {file}: Missing")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  Missing files: {missing_files}")
        print("Please ensure all project files are in the current directory.")
    
    # Check dependencies
    print("\nChecking dependencies...")
    try:
        import cv2
        print("✅ OpenCV: Installed")
    except ImportError:
        print("❌ OpenCV: Missing")
    
    try:
        import tensorflow
        print("✅ TensorFlow: Installed")
    except ImportError:
        print("❌ TensorFlow: Missing")
    
    try:
        import numpy
        print("✅ NumPy: Installed")
    except ImportError:
        print("❌ NumPy: Missing")
    
    # Check model file
    if os.path.exists("emotion_model.h5"):
        print("✅ Emotion model: Found")
    else:
        print("❌ Emotion model: Missing (run option 4 to setup)")

def view_readme():
    """Display README content"""
    print("\n📖 README Content:")
    print("-" * 30)
    
    try:
        with open("README.md", "r", encoding="utf-8") as f:
            content = f.read()
            # Show first 50 lines
            lines = content.split('\n')[:50]
            for line in lines:
                print(line)
            
            if len(content.split('\n')) > 50:
                print("\n... (truncated)")
                print("Open README.md file to see the complete documentation")
    except FileNotFoundError:
        print("❌ README.md not found")
    except Exception as e:
        print(f"❌ Error reading README: {e}")

def main():
    """Main program loop"""
    while True:
        show_menu()
        
        try:
            choice = input("\nEnter your choice (1-7): ").strip()
            
            if choice == '1':
                start_emotion_detection()
            elif choice == '2':
                run_utilities()
            elif choice == '3':
                test_camera()
            elif choice == '4':
                setup_model()
            elif choice == '5':
                check_system_status()
            elif choice == '6':
                view_readme()
            elif choice == '7':
                print("\n👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter a number between 1-7.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ An error occurred: {e}")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    print("🎭 Welcome to Real-Time Emotion Detection System!")
    main()
