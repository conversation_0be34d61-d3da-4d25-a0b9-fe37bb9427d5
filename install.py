#!/usr/bin/env python3
"""
Installation script for Real-Time Emotion Detection System
This script helps users set up the emotion detection system automatically.
"""

import subprocess
import sys
import os
import importlib.util

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required!")
        print(f"Current version: {sys.version}")
        return False
    else:
        print(f"✅ Python version: {sys.version}")
        return True

def install_requirements():
    """Install required packages"""
    print("\n📦 Installing required packages...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        print("Try running: pip install -r requirements.txt manually")
        return False

def check_package_installation():
    """Verify that all required packages are installed"""
    print("\n🔍 Verifying package installation...")
    
    required_packages = {
        'cv2': 'opencv-python',
        'tensorflow': 'tensorflow',
        'numpy': 'numpy',
        'matplotlib': 'matplotlib',
        'PIL': 'pillow',
        'sklearn': 'scikit-learn'
    }
    
    missing_packages = []
    
    for import_name, package_name in required_packages.items():
        try:
            if import_name == 'cv2':
                import cv2
            elif import_name == 'tensorflow':
                import tensorflow
            elif import_name == 'numpy':
                import numpy
            elif import_name == 'matplotlib':
                import matplotlib
            elif import_name == 'PIL':
                from PIL import Image
            elif import_name == 'sklearn':
                import sklearn
            
            print(f"✅ {package_name}: Installed")
        except ImportError:
            print(f"❌ {package_name}: Missing")
            missing_packages.append(package_name)
    
    return len(missing_packages) == 0

def setup_model():
    """Set up the emotion detection model"""
    print("\n🧠 Setting up emotion detection model...")
    
    try:
        subprocess.check_call([sys.executable, "setup_model.py"])
        print("✅ Model setup completed!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error setting up model: {e}")
        return False

def test_camera():
    """Test camera functionality"""
    print("\n📷 Testing camera...")
    
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print("✅ Camera is working!")
                print(f"Frame size: {frame.shape}")
            else:
                print("⚠️  Camera detected but cannot capture frames")
            cap.release()
            return True
        else:
            print("❌ Cannot access camera")
            return False
    except Exception as e:
        print(f"❌ Camera test failed: {e}")
        return False

def create_desktop_shortcut():
    """Create a desktop shortcut (Windows only)"""
    if sys.platform == "win32":
        print("\n🔗 Creating desktop shortcut...")
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "Emotion Detection.lnk")
            target = os.path.join(os.getcwd(), "main.py")
            wDir = os.getcwd()
            icon = target
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = wDir
            shortcut.IconLocation = icon
            shortcut.save()
            
            print("✅ Desktop shortcut created!")
        except ImportError:
            print("⚠️  Cannot create shortcut (winshell not available)")
        except Exception as e:
            print(f"⚠️  Could not create shortcut: {e}")

def main():
    """Main installation process"""
    print("🚀 Real-Time Emotion Detection System - Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Verify installation
    if not check_package_installation():
        print("\n❌ Some packages are missing. Please install them manually.")
        return False
    
    # Setup model
    if not setup_model():
        print("\n⚠️  Model setup failed, but you can try running setup_model.py manually")
    
    # Test camera
    test_camera()
    
    # Create shortcut (Windows only)
    if sys.platform == "win32":
        create_desktop_shortcut()
    
    print("\n" + "=" * 50)
    print("🎉 Installation completed!")
    print("\nNext steps:")
    print("1. Run 'python main.py' to start emotion detection")
    print("2. Run 'python utils.py' for additional tools")
    print("3. Read README.md for detailed usage instructions")
    print("\nControls during detection:")
    print("- Press 'q' to quit")
    print("- Press 's' to save frame")
    print("- Press 'p' to pause/resume")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Installation failed. Please check the errors above.")
        sys.exit(1)
    else:
        print("\n✅ Ready to detect emotions!")
        
        # Ask if user wants to run the program now
        response = input("\nWould you like to start emotion detection now? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            print("\nStarting emotion detection...")
            try:
                subprocess.run([sys.executable, "main.py"])
            except KeyboardInterrupt:
                print("\nEmotion detection stopped.")
            except Exception as e:
                print(f"Error running main program: {e}")
