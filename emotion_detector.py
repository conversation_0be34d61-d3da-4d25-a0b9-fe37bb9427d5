import os
import sys

# Import packages with error handling
try:
    import cv2
except ImportError:
    print("❌ Error: OpenCV (cv2) is not installed.")
    print("Please install it with: pip install opencv-python")
    sys.exit(1)

try:
    import numpy as np
except ImportError:
    print("❌ Error: NumPy is not installed.")
    print("Please install it with: pip install numpy")
    sys.exit(1)

try:
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, Dropout, Flatten, Conv2D, MaxPooling2D
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.preprocessing.image import img_to_array
except ImportError:
    print("❌ Error: TensorFlow is not installed.")
    print("Please install it with: pip install tensorflow")
    sys.exit(1)

class EmotionDetector:
    def __init__(self):
        self.emotion_labels = ['Angry', 'Disgust', 'Fear', 'Happy', 'Neutral', 'Sad', 'Surprise']
        self.face_classifier = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.emotion_classifier = None
        self.build_model()
        
    def build_model(self):
        """Build and compile the emotion recognition model"""
        self.emotion_classifier = Sequential()
        
        self.emotion_classifier.add(Conv2D(32, kernel_size=(3, 3), activation='relu', input_shape=(48, 48, 1)))
        self.emotion_classifier.add(Conv2D(64, kernel_size=(3, 3), activation='relu'))
        self.emotion_classifier.add(MaxPooling2D(pool_size=(2, 2)))
        self.emotion_classifier.add(Dropout(0.25))
        
        self.emotion_classifier.add(Conv2D(128, kernel_size=(3, 3), activation='relu'))
        self.emotion_classifier.add(MaxPooling2D(pool_size=(2, 2)))
        self.emotion_classifier.add(Conv2D(128, kernel_size=(3, 3), activation='relu'))
        self.emotion_classifier.add(MaxPooling2D(pool_size=(2, 2)))
        self.emotion_classifier.add(Dropout(0.25))
        
        self.emotion_classifier.add(Flatten())
        self.emotion_classifier.add(Dense(1024, activation='relu'))
        self.emotion_classifier.add(Dropout(0.5))
        self.emotion_classifier.add(Dense(7, activation='softmax'))
        
        self.emotion_classifier.compile(loss='categorical_crossentropy', optimizer=Adam(learning_rate=0.0001), metrics=['accuracy'])
        
        # Try to load pre-trained weights if available
        if os.path.exists('emotion_model.h5'):
            self.emotion_classifier.load_weights('emotion_model.h5')
            print("Loaded pre-trained emotion model weights")
        else:
            print("No pre-trained weights found. Model will need training or you can download pre-trained weights.")
    
    def preprocess_face(self, face_image):
        """Preprocess face image for emotion prediction"""
        face_image = cv2.resize(face_image, (48, 48))
        face_image = face_image.astype("float") / 255.0
        face_image = img_to_array(face_image)
        face_image = np.expand_dims(face_image, axis=0)
        return face_image
    
    def detect_faces(self, frame):
        """Detect faces in the frame"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_classifier.detectMultiScale(gray, scaleFactor=1.3, minNeighbors=5)
        return faces, gray
    
    def predict_emotion(self, face_roi):
        """Predict emotion from face ROI"""
        try:
            face_roi = self.preprocess_face(face_roi)
            emotion_prediction = self.emotion_classifier.predict(face_roi)
            emotion_probability = np.max(emotion_prediction)
            emotion_label_arg = np.argmax(emotion_prediction)
            emotion_text = self.emotion_labels[emotion_label_arg]
            return emotion_text, emotion_probability
        except Exception as e:
            print(f"Error in emotion prediction: {e}")
            return "Unknown", 0.0
    
    def draw_emotion_results(self, frame, faces, gray):
        """Draw bounding boxes and emotion labels on the frame"""
        for (x, y, w, h) in faces:
            cv2.rectangle(frame, (x, y-50), (x+w, y+h+10), (255, 0, 0), 2)
            roi_gray = gray[y:y+h, x:x+w]
            
            emotion_text, emotion_probability = self.predict_emotion(roi_gray)
            
            # Display emotion and confidence
            cv2.putText(frame, f'{emotion_text}: {emotion_probability:.2f}', 
                       (x+20, y-60), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2, cv2.LINE_AA)
            
            # Color code based on emotion
            color = self.get_emotion_color(emotion_text)
            cv2.rectangle(frame, (x, y-50), (x+w, y+h+10), color, 2)
        
        return frame
    
    def get_emotion_color(self, emotion):
        """Get color based on emotion for visualization"""
        color_map = {
            'Happy': (0, 255, 0),      # Green
            'Sad': (255, 0, 0),        # Blue
            'Angry': (0, 0, 255),      # Red
            'Surprise': (255, 255, 0), # Cyan
            'Fear': (128, 0, 128),     # Purple
            'Disgust': (0, 128, 128),  # Olive
            'Neutral': (128, 128, 128) # Gray
        }
        return color_map.get(emotion, (255, 255, 255))  # Default white
