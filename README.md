# Real-Time Emotion Detection System

A Python-based real-time emotion detection system that uses computer vision and deep learning to recognize human emotions from facial expressions through a webcam.

## Features

- **Real-time emotion detection** from webcam feed
- **7 emotion categories**: Angry, <PERSON>s<PERSON><PERSON>, <PERSON>, <PERSON>, Neutral, Sad, Surprise
- **Face detection** using OpenCV Haar cascades
- **Deep learning model** with CNN architecture
- **Visual feedback** with colored bounding boxes and confidence scores
- **Performance monitoring** with FPS counter
- **Frame saving** functionality
- **Pause/resume** capability
- **Utility tools** for testing and calibration

## Detected Emotions

The system can detect and classify the following emotions:
- 😠 **Angry** - Red bounding box
- 🤢 **Disgust** - Olive bounding box  
- 😨 **Fear** - Purple bounding box
- 😊 **Happy** - Green bounding box
- 😐 **Neutral** - Gray bounding box
- 😢 **Sad** - Blue bounding box
- 😲 **Surprise** - Cyan bounding box

## Installation

### Prerequisites
- Python 3.7 or higher
- Webcam/Camera
- Windows/Linux/macOS

### Quick Start (Recommended)
```bash
python start.py
```
This will automatically check dependencies, install missing packages, and guide you through setup.

### Alternative: Step-by-Step Installation

#### Step 1: Check Setup
```bash
python check_setup.py
```

#### Step 2: Install Dependencies (if needed)
```bash
pip install -r requirements.txt
```

#### Step 3: Set up the Model
```bash
python setup_model.py
```

#### Step 4: Test Your Setup
```bash
python utils.py
```
Choose option 1 to test your camera and option 3 to check dependencies.

## Usage

### Basic Usage
Run the main emotion detection program:
```bash
python main.py
```

### Command Line Options
```bash
# Use a specific camera (if you have multiple cameras)
python main.py --camera 1

# Test camera connection
python main.py --test
```

### Controls During Detection
- **'q'** - Quit the application
- **'s'** - Save current frame with timestamp
- **'p'** - Pause/resume detection

### Utility Functions
Run the utility script for additional tools:
```bash
python utils.py
```

Available utilities:
1. **Test camera** - Check if your camera is working
2. **List available cameras** - Find all connected cameras
3. **Check dependencies** - Verify all packages are installed
4. **Calibrate detection sensitivity** - Adjust face detection parameters

## File Structure

```
emotion-detection/
├── start.py               # Safe startup script (recommended)
├── check_setup.py         # Setup checker and installer
├── main.py                # Main application
├── emotion_detector.py    # Core emotion detection class
├── setup_model.py         # Model setup and initialization
├── utils.py               # Utility functions and tools
├── run.py                 # Interactive menu system
├── install.py             # Full installation script
├── train_model.py         # Training script template
├── requirements.txt       # Python dependencies
├── README.md              # This file
└── emotion_model.h5       # Model weights (created after setup)
```

## How It Works

1. **Camera Capture**: Captures video frames from your webcam
2. **Face Detection**: Uses OpenCV's Haar cascade classifier to detect faces
3. **Preprocessing**: Resizes and normalizes detected face regions
4. **Emotion Prediction**: Uses a CNN model to classify emotions
5. **Visualization**: Displays results with colored bounding boxes and labels

## Model Architecture

The emotion detection model uses a Convolutional Neural Network (CNN) with:
- Multiple convolutional layers with ReLU activation
- Max pooling layers for dimensionality reduction
- Dropout layers for regularization
- Dense layers for final classification
- Softmax output for 7 emotion categories

## Improving Accuracy

For better emotion detection accuracy:

1. **Train with your own data**:
   ```bash
   python train_model.py
   ```

2. **Download pre-trained weights** from emotion recognition datasets like FER2013

3. **Adjust lighting conditions** for better face detection

4. **Calibrate detection sensitivity** using the utility tools

## Troubleshooting

### Camera Issues
- **Camera not found**: Try different camera indices (0, 1, 2...)
- **Permission denied**: Check camera permissions in your OS
- **Poor quality**: Ensure good lighting and camera positioning

### Performance Issues
- **Low FPS**: Reduce frame size or use a faster computer
- **High CPU usage**: Close other applications
- **Memory issues**: Restart the application periodically

### Detection Issues
- **No faces detected**: Improve lighting, face the camera directly
- **Wrong emotions**: The model may need training with more data
- **Multiple faces**: The system detects all visible faces

## Technical Requirements

- **CPU**: Modern multi-core processor recommended
- **RAM**: Minimum 4GB, 8GB recommended
- **Camera**: Any USB webcam or built-in camera
- **OS**: Windows 10+, macOS 10.14+, or Linux

## Future Enhancements

Potential improvements for the system:
- Age and gender detection
- Emotion intensity measurement
- Multiple face tracking
- Data logging and analytics
- Mobile app version
- Cloud-based processing

## Contributing

Feel free to contribute to this project by:
- Reporting bugs
- Suggesting new features
- Improving the model accuracy
- Adding new emotion categories
- Optimizing performance

## License

This project is open source and available under the MIT License.

## Acknowledgments

- OpenCV for computer vision capabilities
- TensorFlow/Keras for deep learning framework
- FER2013 dataset for emotion recognition research
