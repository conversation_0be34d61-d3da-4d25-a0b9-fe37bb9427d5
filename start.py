#!/usr/bin/env python3
"""
Safe startup script for Real-Time Emotion Detection System
This script handles common issues and provides helpful error messages.
"""

import sys
import os
import subprocess

def print_banner():
    """Print welcome banner"""
    print("🎭 Real-Time Emotion Detection System")
    print("=" * 40)
    print("Safe Startup Script")
    print("=" * 40)

def check_files():
    """Check if required files exist"""
    required_files = [
        'main.py',
        'emotion_detector.py', 
        'requirements.txt',
        'utils.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        print("Please ensure all project files are in the current directory.")
        return False
    
    print("✅ All required files found")
    return True

def quick_dependency_check():
    """Quick check for critical dependencies"""
    print("\n🔍 Quick dependency check...")
    
    critical_packages = ['cv2', 'numpy', 'tensorflow']
    missing = []
    
    for package in critical_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'numpy':
                import numpy
            elif package == 'tensorflow':
                import tensorflow
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing.append(package)
    
    return len(missing) == 0, missing

def offer_installation(missing_packages):
    """Offer to install missing packages"""
    print(f"\n📦 Missing packages detected: {missing_packages}")
    print("\nOptions:")
    print("1. Auto-install dependencies (recommended)")
    print("2. Run setup checker")
    print("3. Exit and install manually")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == '1':
        return install_dependencies()
    elif choice == '2':
        return run_setup_checker()
    else:
        print("\nManual installation:")
        print("pip install -r requirements.txt")
        return False

def install_dependencies():
    """Install dependencies automatically"""
    print("\n📥 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.PIPE)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        print("Please try manual installation: pip install -r requirements.txt")
        return False

def run_setup_checker():
    """Run the setup checker script"""
    print("\n🔧 Running setup checker...")
    
    try:
        subprocess.run([sys.executable, "check_setup.py"])
        return True
    except FileNotFoundError:
        print("❌ check_setup.py not found")
        return False
    except Exception as e:
        print(f"❌ Error running setup checker: {e}")
        return False

def setup_model():
    """Set up the emotion detection model"""
    print("\n🧠 Setting up emotion detection model...")
    
    if os.path.exists('emotion_model.h5'):
        print("✅ Model already exists")
        return True
    
    try:
        subprocess.run([sys.executable, "setup_model.py"], 
                      stdout=subprocess.DEVNULL, stderr=subprocess.PIPE)
        print("✅ Model setup complete")
        return True
    except Exception as e:
        print(f"⚠️  Model setup failed: {e}")
        print("You can try running: python setup_model.py manually")
        return False

def start_application():
    """Start the main application"""
    print("\n🚀 Starting emotion detection...")
    print("Controls: 'q' to quit, 's' to save frame, 'p' to pause")
    print("Press Ctrl+C to stop if needed\n")
    
    try:
        subprocess.run([sys.executable, "main.py"])
        return True
    except KeyboardInterrupt:
        print("\n✋ Application stopped by user")
        return True
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        return False

def show_menu():
    """Show main menu"""
    print("\n" + "=" * 40)
    print("What would you like to do?")
    print("1. 🚀 Start Emotion Detection")
    print("2. 🔧 Run Utilities")
    print("3. 📊 Check Setup")
    print("4. 🧠 Setup Model")
    print("5. ❌ Exit")
    print("=" * 40)
    
    choice = input("Enter your choice (1-5): ").strip()
    return choice

def main():
    """Main function"""
    print_banner()
    
    # Check files
    if not check_files():
        return False
    
    # Quick dependency check
    deps_ok, missing = quick_dependency_check()
    
    if not deps_ok:
        if not offer_installation(missing):
            return False
        
        # Re-check after installation
        deps_ok, missing = quick_dependency_check()
        if not deps_ok:
            print(f"\n❌ Still missing dependencies: {missing}")
            return False
    
    # Setup model if needed
    setup_model()
    
    # Main menu loop
    while True:
        choice = show_menu()
        
        if choice == '1':
            start_application()
        elif choice == '2':
            try:
                subprocess.run([sys.executable, "utils.py"])
            except Exception as e:
                print(f"❌ Error running utilities: {e}")
        elif choice == '3':
            run_setup_checker()
        elif choice == '4':
            setup_model()
        elif choice == '5':
            print("\n👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please try again.")
        
        input("\nPress Enter to continue...")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ Startup failed. Please check the errors above.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please report this issue.")
        sys.exit(1)
