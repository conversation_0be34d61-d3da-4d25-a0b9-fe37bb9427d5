#!/usr/bin/env python3
"""
Quick setup checker for Real-Time Emotion Detection System
Run this first to check if everything is properly installed.
"""

import sys
import subprocess

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    if sys.version_info < (3, 7):
        print(f"❌ Python 3.7+ required. Current: {sys.version}")
        return False
    else:
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True

def check_dependencies():
    """Check if required packages are installed"""
    print("\n📦 Checking dependencies...")
    
    dependencies = {
        'opencv-python': 'cv2',
        'numpy': 'numpy',
        'tensorflow': 'tensorflow',
        'matplotlib': 'matplotlib',
        'pillow': 'PIL'
    }
    
    missing = []
    installed = []
    
    for package, import_name in dependencies.items():
        try:
            if import_name == 'cv2':
                import cv2
            elif import_name == 'numpy':
                import numpy
            elif import_name == 'tensorflow':
                import tensorflow
            elif import_name == 'matplotlib':
                import matplotlib
            elif import_name == 'PIL':
                from PIL import Image
            
            print(f"✅ {package}")
            installed.append(package)
        except ImportError:
            print(f"❌ {package}")
            missing.append(package)
    
    return missing, installed

def install_missing_packages(missing_packages):
    """Install missing packages"""
    if not missing_packages:
        return True
    
    print(f"\n📥 Installing missing packages: {', '.join(missing_packages)}")
    
    try:
        # Try installing from requirements.txt first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ Packages installed successfully!")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        # Fallback to individual package installation
        try:
            for package in missing_packages:
                print(f"Installing {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package],
                                    stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print("✅ Packages installed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install packages: {e}")
            return False

def test_imports():
    """Test if all imports work after installation"""
    print("\n🧪 Testing imports...")
    
    try:
        import cv2
        import numpy as np
        import tensorflow
        from tensorflow.keras.models import Sequential
        print("✅ All imports successful!")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def check_camera():
    """Quick camera test"""
    print("\n📷 Testing camera access...")
    
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print("✅ Camera is working!")
                cap.release()
                return True
            else:
                print("⚠️  Camera detected but cannot capture frames")
                cap.release()
                return False
        else:
            print("❌ Cannot access camera")
            return False
    except Exception as e:
        print(f"❌ Camera test failed: {e}")
        return False

def main():
    """Main setup checker"""
    print("🎭 Real-Time Emotion Detection - Setup Checker")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        print("\n❌ Please upgrade Python and try again.")
        return False
    
    # Check dependencies
    missing, installed = check_dependencies()
    
    if missing:
        print(f"\n⚠️  Missing packages detected: {missing}")
        response = input("Would you like to install them now? (y/n): ").lower().strip()
        
        if response in ['y', 'yes']:
            if install_missing_packages(missing):
                # Re-check after installation
                print("\n🔄 Re-checking dependencies...")
                missing, installed = check_dependencies()
                
                if missing:
                    print(f"\n❌ Still missing: {missing}")
                    print("Please install manually with: pip install -r requirements.txt")
                    return False
            else:
                print("\n❌ Installation failed. Please install manually.")
                return False
        else:
            print("\n❌ Cannot proceed without required packages.")
            return False
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed. Please check your installation.")
        return False
    
    # Test camera
    camera_ok = check_camera()
    
    print("\n" + "=" * 50)
    if camera_ok:
        print("🎉 Setup complete! Everything is working.")
        print("\nYou can now run:")
        print("• python main.py - Start emotion detection")
        print("• python run.py - Interactive menu")
        print("• python utils.py - Utility tools")
    else:
        print("⚠️  Setup mostly complete, but camera issues detected.")
        print("Please check your camera connection and permissions.")
        print("\nYou can still run:")
        print("• python utils.py - Use utility tools to diagnose camera issues")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ Setup incomplete. Please resolve the issues above.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Setup cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
